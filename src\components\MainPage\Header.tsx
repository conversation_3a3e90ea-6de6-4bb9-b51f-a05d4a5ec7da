"use client"

import React, { useState } from 'react'

interface HeaderProps {
  searchQuery: string
  onSearchChange: (query: string) => void
}

const Header: React.FC<HeaderProps> = ({ searchQuery, onSearchChange }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  return (
    <header className="p-4 md:p-6">
      {/* Main Header Row */}
      <div className="flex items-center justify-between">
        {/* Logo */}
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-secondary-500 rounded flex items-center justify-center">
            <span className="text-foreground-primary font-bold">🎮</span>
          </div>
          <span className="text-xl font-bold">VGzone</span>
        </div>

        {/* Desktop Search Bar - Hidden on mobile */}
        <div className="hidden md:block absolute left-1/2 -translate-x-1/2">
          <input
            type="text"
            placeholder="Search games..."
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            className="bg-background-card rounded-full px-6 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-secondary-400 w-80"
          />
        </div>

        {/* Desktop Navigation - Hidden on mobile */}
        <nav className="hidden md:flex space-x-6">
          <a href="/community" className="flex items-center space-x-1 hover:text-pink-400 transition-colors">
            <span>👥</span>
            <span>Community</span>
          </a>
          <a href="/store" className="flex items-center space-x-1 hover:text-pink-400 transition-colors">
            <span>🏪</span>
            <span>Store</span>
          </a>
        </nav>

        {/* Mobile Menu Button */}
        <button 
          onClick={() => setIsMenuOpen(!isMenuOpen)}
          className="md:hidden p-2 hover:bg-white/10 rounded-lg transition-colors"
          aria-label="Toggle menu"
        >
          <svg 
            className="w-6 h-6" 
            fill="none" 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth="2" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            {isMenuOpen ? (
              <path d="M6 18L18 6M6 6l12 12" />
            ) : (
              <path d="M4 6h16M4 12h16M4 18h16" />
            )}
          </svg>
        </button>
      </div>

      {/* Mobile Search Bar - Below header on mobile */}
      <div className="md:hidden mt-4">
        <input 
          type="text" 
          placeholder="Search games..." 
          value={searchQuery}
          onChange={(e) => onSearchChange(e.target.value)}
          className="bg-gray-800 rounded-full px-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-pink-400 w-full"
        />
      </div>

      {/* Mobile Menu - Dropdown */}
      {isMenuOpen && (
        <nav className="md:hidden mt-4 space-y-2 bg-gray-800/50 rounded-lg p-4 backdrop-blur-sm">
          <a 
            href="/community" 
            className="flex items-center space-x-2 hover:text-pink-400 transition-colors py-2 px-2 hover:bg-white/10 rounded"
            onClick={() => setIsMenuOpen(false)}
          >
            <span>👥</span>
            <span>Community</span>
          </a>
          <a 
            href="/store" 
            className="flex items-center space-x-2 hover:text-pink-400 transition-colors py-2 px-2 hover:bg-white/10 rounded"
            onClick={() => setIsMenuOpen(false)}
          >
            <span>🏪</span>
            <span>Store</span>
          </a>
        </nav>
      )}
    </header>
  )
}

export default Header
