import React from 'react'

const ColorTest = () => {
  return (
    <div className="p-8 space-y-4">
      <h2 className="text-2xl font-bold mb-6">Custom Color Test</h2>
      
      {/* Test Primary Colors */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Primary Colors</h3>
        <div className="flex space-x-2">
          <div className="w-16 h-16 bg-primary-500 rounded flex items-center justify-center text-white text-xs">500</div>
          <div className="w-16 h-16 bg-primary-600 rounded flex items-center justify-center text-white text-xs">600</div>
          <div className="w-16 h-16 bg-primary-700 rounded flex items-center justify-center text-white text-xs">700</div>
          <div className="w-16 h-16 bg-primary-800 rounded flex items-center justify-center text-white text-xs">800</div>
          <div className="w-16 h-16 bg-primary-900 rounded flex items-center justify-center text-white text-xs">900</div>
        </div>
      </div>

      {/* Test Secondary Colors */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Secondary Colors</h3>
        <div className="flex space-x-2">
          <div className="w-16 h-16 bg-secondary-500 rounded flex items-center justify-center text-white text-xs">500</div>
          <div className="w-16 h-16 bg-secondary-600 rounded flex items-center justify-center text-white text-xs">600</div>
          <div className="w-16 h-16 bg-secondary-700 rounded flex items-center justify-center text-white text-xs">700</div>
          <div className="w-16 h-16 bg-secondary-800 rounded flex items-center justify-center text-white text-xs">800</div>
          <div className="w-16 h-16 bg-secondary-900 rounded flex items-center justify-center text-white text-xs">900</div>
        </div>
      </div>

      {/* Test Background Colors */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Background Colors</h3>
        <div className="flex space-x-2">
          <div className="w-16 h-16 bg-bg-primary rounded border border-gray-300 flex items-center justify-center text-white text-xs">primary</div>
          <div className="w-16 h-16 bg-bg-secondary rounded flex items-center justify-center text-white text-xs">secondary</div>
          <div className="w-16 h-16 bg-bg-tertiary rounded flex items-center justify-center text-white text-xs">tertiary</div>
          <div className="w-16 h-16 bg-bg-card rounded border border-gray-300 flex items-center justify-center text-white text-xs">card</div>
        </div>
      </div>

      {/* Test Foreground Colors */}
      <div className="space-y-2 bg-gray-800 p-4 rounded">
        <h3 className="text-lg font-semibold text-white">Foreground Colors</h3>
        <div className="space-y-1">
          <p className="text-text-primary">Primary text (should be white)</p>
          <p className="text-text-secondary">Secondary text (should be gray-300)</p>
          <p className="text-text-tertiary">Tertiary text (should be gray-400)</p>
          <p className="text-text-muted">Muted text (should be gray-500)</p>
        </div>
      </div>

      {/* Test Status Colors */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Status Colors</h3>
        <div className="flex space-x-2">
          <div className="w-16 h-16 bg-success-500 rounded flex items-center justify-center text-white text-xs">success</div>
          <div className="w-16 h-16 bg-error-500 rounded flex items-center justify-center text-white text-xs">error</div>
          <div className="w-16 h-16 bg-warning-500 rounded flex items-center justify-center text-black text-xs">warning</div>
          <div className="w-16 h-16 bg-info-500 rounded flex items-center justify-center text-white text-xs">info</div>
        </div>
      </div>

      {/* Test Accent Colors */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Accent Colors (Individual)</h3>
        <div className="flex space-x-2">
          <div className="w-16 h-16 bg-accent-warm-pink-from rounded flex items-center justify-center text-white text-xs">pink</div>
          <div className="w-16 h-16 bg-accent-warm-red-from rounded flex items-center justify-center text-white text-xs">red</div>
          <div className="w-16 h-16 bg-accent-cool-blue-from rounded flex items-center justify-center text-white text-xs">blue</div>
          <div className="w-16 h-16 bg-accent-nature-green-from rounded flex items-center justify-center text-white text-xs">green</div>
        </div>
      </div>

      {/* Test Gradient */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Gradient Test</h3>
        <div className="w-full h-16 bg-gradient-to-r from-primary-500 to-secondary-500 rounded flex items-center justify-center text-white mb-2">
          Primary to Secondary Gradient
        </div>
        <div className="w-full h-16 bg-gradient-to-r from-accent-warm-pink-from to-accent-warm-pink-to rounded flex items-center justify-center text-white">
          Accent Gradient (Pink to Yellow)
        </div>
      </div>
    </div>
  )
}

export default ColorTest
