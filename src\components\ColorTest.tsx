import React from 'react'

const ColorTest = () => {
  return (
    <div className="p-8 space-y-4">
      <h2 className="text-2xl font-bold mb-6">Custom Color Test</h2>
      
      {/* Test Primary Colors */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Primary Colors</h3>
        <div className="flex space-x-2">
          <div className="w-16 h-16 bg-primary-500 rounded flex items-center justify-center text-white text-xs">500</div>
          <div className="w-16 h-16 bg-primary-600 rounded flex items-center justify-center text-white text-xs">600</div>
          <div className="w-16 h-16 bg-primary-700 rounded flex items-center justify-center text-white text-xs">700</div>
          <div className="w-16 h-16 bg-primary-800 rounded flex items-center justify-center text-white text-xs">800</div>
          <div className="w-16 h-16 bg-primary-900 rounded flex items-center justify-center text-white text-xs">900</div>
        </div>
      </div>

      {/* Test Secondary Colors */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Secondary Colors</h3>
        <div className="flex space-x-2">
          <div className="w-16 h-16 bg-secondary-500 rounded flex items-center justify-center text-white text-xs">500</div>
          <div className="w-16 h-16 bg-secondary-600 rounded flex items-center justify-center text-white text-xs">600</div>
          <div className="w-16 h-16 bg-secondary-700 rounded flex items-center justify-center text-white text-xs">700</div>
          <div className="w-16 h-16 bg-secondary-800 rounded flex items-center justify-center text-white text-xs">800</div>
          <div className="w-16 h-16 bg-secondary-900 rounded flex items-center justify-center text-white text-xs">900</div>
        </div>
      </div>

      {/* Test Background Colors */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Background Colors</h3>
        <div className="flex space-x-2">
          <div className="w-16 h-16 bg-background-primary rounded border border-gray-300 flex items-center justify-center text-white text-xs">primary</div>
          <div className="w-16 h-16 bg-background-secondary rounded flex items-center justify-center text-white text-xs">secondary</div>
          <div className="w-16 h-16 bg-background-tertiary rounded flex items-center justify-center text-white text-xs">tertiary</div>
          <div className="w-16 h-16 bg-background-card rounded border border-gray-300 flex items-center justify-center text-white text-xs">card</div>
        </div>
      </div>

      {/* Test Foreground Colors */}
      <div className="space-y-2 bg-gray-800 p-4 rounded">
        <h3 className="text-lg font-semibold text-white">Foreground Colors</h3>
        <div className="space-y-1">
          <p className="text-foreground-primary">Primary text (should be white)</p>
          <p className="text-foreground-secondary">Secondary text (should be gray-300)</p>
          <p className="text-foreground-tertiary">Tertiary text (should be gray-400)</p>
          <p className="text-foreground-muted">Muted text (should be gray-500)</p>
        </div>
      </div>

      {/* Test Status Colors */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Status Colors</h3>
        <div className="flex space-x-2">
          <div className="w-16 h-16 bg-success-500 rounded flex items-center justify-center text-white text-xs">success</div>
          <div className="w-16 h-16 bg-error-500 rounded flex items-center justify-center text-white text-xs">error</div>
          <div className="w-16 h-16 bg-warning-500 rounded flex items-center justify-center text-black text-xs">warning</div>
          <div className="w-16 h-16 bg-info-500 rounded flex items-center justify-center text-white text-xs">info</div>
        </div>
      </div>

      {/* Test Gradient */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Gradient Test</h3>
        <div className="w-full h-16 bg-gradient-to-r from-primary-500 to-secondary-500 rounded flex items-center justify-center text-white">
          Primary to Secondary Gradient
        </div>
      </div>
    </div>
  )
}

export default ColorTest
