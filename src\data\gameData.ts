export const GAME_CARDS = [
  {
    id: 1,
    name: '<PERSON>',
    subtitle: 'Dream Land',
    emoji: '🌟',
    rating: 4.2,
    bgGradient: 'from-pink-400 to-yellow-400',
    textColor: 'text-black',
    shadowColor: 'shadow-pink-500/30',
    hoverShadow: 'hover:shadow-pink-500/30'
  },
  {
    id: 2,
    name: 'Toad',
    subtitle: 'Kingdom Battle',
    emoji: '🍄',
    rating: 4.1,
    bgGradient: 'from-purple-600 to-purple-800',
    textColor: 'text-white',
    shadowColor: 'shadow-purple-500/30',
    hoverShadow: 'hover:shadow-purple-500/30'
  },
  {
    id: 3,
    name: '<PERSON><PERSON> <PERSON>',
    subtitle: 'Pro Gost',
    emoji: '👻',
    rating: 4.1,
    bgGradient: 'from-red-500 to-purple-700',
    textColor: 'text-white',
    shadowColor: 'shadow-red-500/40',
    hoverShadow: 'hover:shadow-red-500/40'
  },
  {
    id: 4,
    name: '<PERSON><PERSON><PERSON>',
    subtitle: 'Star Allies',
    emoji: '🦕',
    rating: 4.1,
    bgGradient: 'from-blue-600 to-purple-800',
    textColor: 'text-white',
    shadowColor: 'shadow-blue-500/30',
    hoverShadow: 'hover:shadow-blue-500/30'
  },
  {
    id: 5,
    name: 'Wario',
    subtitle: 'Royale G',
    emoji: '👨',
    rating: 4.0,
    bgGradient: 'from-purple-600 to-purple-800',
    textColor: 'text-white',
    shadowColor: 'shadow-purple-500/30',
    hoverShadow: 'hover:shadow-purple-500/30'
  },
  {
    id: 6,
    name: 'Mario',
    subtitle: 'Odyssey',
    emoji: '🔴',
    rating: 4.8,
    bgGradient: 'from-red-600 to-red-800',
    textColor: 'text-white',
    shadowColor: 'shadow-red-500/30',
    hoverShadow: 'hover:shadow-red-500/30'
  },
  {
    id: 7,
    name: 'Luigi',
    subtitle: 'Mansion 3',
    emoji: '💚',
    rating: 4.5,
    bgGradient: 'from-green-500 to-green-700',
    textColor: 'text-white',
    shadowColor: 'shadow-green-500/30',
    hoverShadow: 'hover:shadow-green-500/30'
  },
  {
    id: 8,
    name: 'Yoshi',
    subtitle: 'Crafted World',
    emoji: '🥚',
    rating: 4.3,
    bgGradient: 'from-lime-400 to-green-600',
    textColor: 'text-white',
    shadowColor: 'shadow-lime-500/30',
    hoverShadow: 'hover:shadow-lime-500/30'
  },
  {
    id: 9,
    name: 'Peach',
    subtitle: 'Princess Power',
    emoji: '👑',
    rating: 4.4,
    bgGradient: 'from-pink-500 to-pink-700',
    textColor: 'text-white',
    shadowColor: 'shadow-pink-500/30',
    hoverShadow: 'hover:shadow-pink-500/30'
  },
  {
    id: 10,
    name: 'Bowser',
    subtitle: 'Fury Mode',
    emoji: '🔥',
    rating: 4.6,
    bgGradient: 'from-orange-600 to-red-700',
    textColor: 'text-white',
    shadowColor: 'shadow-orange-500/30',
    hoverShadow: 'hover:shadow-orange-500/30'
  },
  {
    id: 11,
    name: 'Link',
    subtitle: 'Wild Adventure',
    emoji: '⚔️',
    rating: 4.9,
    bgGradient: 'from-teal-500 to-blue-700',
    textColor: 'text-white',
    shadowColor: 'shadow-teal-500/30',
    hoverShadow: 'hover:shadow-teal-500/30'
  },
  {
    id: 12,
    name: 'Zelda',
    subtitle: 'Tears Kingdom',
    emoji: '✨',
    rating: 4.9,
    bgGradient: 'from-indigo-400 to-purple-600',
    textColor: 'text-white',
    shadowColor: 'shadow-indigo-500/30',
    hoverShadow: 'hover:shadow-indigo-500/30'
  },
  {
    id: 13,
    name: 'Samus',
    subtitle: 'Dread',
    emoji: '🤖',
    rating: 4.7,
    bgGradient: 'from-orange-500 to-red-600',
    textColor: 'text-white',
    shadowColor: 'shadow-orange-500/30',
    hoverShadow: 'hover:shadow-orange-500/30'
  },
  {
    id: 14,
    name: 'Fox',
    subtitle: 'Star Command',
    emoji: '🦊',
    rating: 4.2,
    bgGradient: 'from-gray-600 to-gray-800',
    textColor: 'text-white',
    shadowColor: 'shadow-gray-500/30',
    hoverShadow: 'hover:shadow-gray-500/30'
  },
  {
    id: 15,
    name: 'Donkey Kong',
    subtitle: 'Tropical Freeze',
    emoji: '🦍',
    rating: 4.5,
    bgGradient: 'from-amber-600 to-brown-700',
    textColor: 'text-white',
    shadowColor: 'shadow-amber-500/30',
    hoverShadow: 'hover:shadow-amber-500/30'
  },
  {
    id: 16,
    name: 'Pikachu',
    subtitle: 'Electric Tale',
    emoji: '⚡',
    rating: 4.8,
    bgGradient: 'from-yellow-400 to-orange-500',
    textColor: 'text-black',
    shadowColor: 'shadow-yellow-500/30',
    hoverShadow: 'hover:shadow-yellow-500/30'
  },
  {
    id: 17,
    name: 'Splatoon',
    subtitle: 'Ink Battle',
    emoji: '🎨',
    rating: 4.6,
    bgGradient: 'from-cyan-400 to-purple-600',
    textColor: 'text-white',
    shadowColor: 'shadow-cyan-500/30',
    hoverShadow: 'hover:shadow-cyan-500/30'
  },
  {
    id: 18,
    name: 'Isabelle',
    subtitle: 'Animal Crossing',
    emoji: '🏝️',
    rating: 4.7,
    bgGradient: 'from-green-400 to-teal-600',
    textColor: 'text-white',
    shadowColor: 'shadow-green-500/30',
    hoverShadow: 'hover:shadow-green-500/30'
  },
  {
    id: 19,
    name: 'Villager',
    subtitle: 'New Horizons',
    emoji: '🌳',
    rating: 4.7,
    bgGradient: 'from-emerald-400 to-green-700',
    textColor: 'text-white',
    shadowColor: 'shadow-emerald-500/30',
    hoverShadow: 'hover:shadow-emerald-500/30'
  },
  {
    id: 20,
    name: 'Mega Man',
    subtitle: 'Legacy',
    emoji: '🤖',
    rating: 4.4,
    bgGradient: 'from-blue-500 to-cyan-700',
    textColor: 'text-white',
    shadowColor: 'shadow-blue-500/30',
    hoverShadow: 'hover:shadow-blue-500/30'
  }
]
