import React from 'react'

interface GameCardProps {
  name: string
  subtitle: string
  emoji: string
  rating: number
  bgGradient: string
  textColor: string
  shadowColor: string
  hoverShadow: string
}

const GameCard: React.FC<GameCardProps> = ({
  name,
  subtitle,
  emoji,
  rating,
  bgGradient,
  textColor,
  hoverShadow
}) => {
  return (
    <div
      className={`bg-gradient-to-b ${bgGradient} rounded-3xl p-6 w-48 h-64 flex-shrink-0 flex flex-col items-center justify-center transition-all duration-300 ease-in-out hover:scale-110 hover:shadow-2xl ${hoverShadow} cursor-pointer transform hover:-translate-y-2 snap-center`}
    >
      <div className="text-6xl mb-2 transition-transform duration-300 hover:scale-110">
        {emoji}
      </div>
      <div className={`${textColor} font-bold text-lg`}>
        {name}
      </div>
      <div className={`${textColor === 'text-black' ? 'text-black' : 'text-gray-300'} text-sm`}>
        {subtitle}
      </div>
      {rating && (
        <div className="flex items-center mt-2">
          <span className={textColor === 'text-black' ? 'text-yellow-600' : 'text-yellow-400'}>👑</span>
          <span className={`${textColor} ml-1`}>{rating}</span>
        </div>
      )}
    </div>
  )
}

export default GameCard
