"use client"

import React, { useState, useRef } from 'react';
import Image from 'next/image';

interface UploadedImage {
  filename: string;
  url: string;
  uploadDate: string;
  name?: string;
  mobile?: string;
  description?: string;
  rating?: number;
}

interface ImageUploadProps {
  onImageUploaded?: (image: UploadedImage) => void;
}

const ImageUpload: React.FC<ImageUploadProps> = ({ onImageUploaded }) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [uploadSuccess, setUploadSuccess] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  
  // Form state
  const [name, setName] = useState('');
  const [mobile, setMobile] = useState('');
  const [description, setDescription] = useState('');
  const [rating, setRating] = useState<number>(0);
  const [hoveredStar, setHoveredStar] = useState<number>(0);
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      prepareFileForUpload(files[0]);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      prepareFileForUpload(files[0]);
    }
  };

  const prepareFileForUpload = (file: File) => {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      setUploadError('Please upload only image files (JPG, PNG, GIF, WebP)');
      return;
    }

    // Validate file size (5MB max)
    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
      setUploadError('File size must be less than 5MB');
      return;
    }

    setSelectedFile(file);
    setUploadError(null);
    
    // Create preview URL
    const url = URL.createObjectURL(file);
    setPreviewUrl(url);
    setShowForm(true);
  };

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedFile) {
      setUploadError('Please select a file first');
      return;
    }

    if (!name.trim() || !mobile.trim() || rating === 0) {
      setUploadError('Please fill in all required fields and provide a rating');
      return;
    }

    setIsUploading(true);
    setUploadError(null);
    setUploadSuccess(null);

    try {
      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('name', name.trim());
      formData.append('mobile', mobile.trim());
      formData.append('description', description.trim());
      formData.append('rating', rating.toString());

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (response.ok) {
        setUploadSuccess(data.message);
        if (onImageUploaded) {
          onImageUploaded({
            filename: data.filename,
            url: data.url,
            uploadDate: new Date().toISOString(),
            name: name.trim(),
            mobile: mobile.trim(),
            description: description.trim(),
            rating: rating
          });
        }
        // Reset form
        resetForm();
      } else {
        setUploadError(data.error || 'Upload failed');
      }
    } catch (error) {
      setUploadError('Upload failed. Please try again.');
      console.error('Upload error:', error);
    } finally {
      setIsUploading(false);
    }
  };

  const resetForm = () => {
    setSelectedFile(null);
    setPreviewUrl(null);
    setShowForm(false);
    setName('');
    setMobile('');
    setDescription('');
    setRating(0);
    setHoveredStar(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
  };

  const cancelUpload = () => {
    resetForm();
    setUploadError(null);
    setUploadSuccess(null);
  };

  const openFileDialog = () => {
    if (fileInputRef.current && !showForm) {
      fileInputRef.current.click();
    }
  };

  const renderStarRating = () => {
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            className={`text-2xl transition-colors ${
              star <= (hoveredStar || rating) 
                ? 'text-yellow-400' 
                : 'text-gray-600 hover:text-yellow-300'
            }`}
            onMouseEnter={() => setHoveredStar(star)}
            onMouseLeave={() => setHoveredStar(0)}
            onClick={() => setRating(star)}
          >
            ★
          </button>
        ))}
        <span className="ml-2 text-sm text-gray-400">
          {rating > 0 && `${rating} star${rating > 1 ? 's' : ''}`}
        </span>
      </div>
    );
  };

  return (
    <div className="w-full">
      {!showForm ? (
        // Upload Area
        <div
          className={`
            relative border-2 border-dashed rounded-xl p-8 text-center transition-all cursor-pointer
            ${isDragging 
              ? 'border-purple-400 bg-purple-400/10' 
              : 'border-gray-400 hover:border-purple-400 hover:bg-white/5'
            }
          `}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={openFileDialog}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileSelect}
            className="hidden"
          />

          <div className="flex flex-col items-center space-y-4">
            <div className="w-16 h-16 bg-purple-500/20 rounded-full flex items-center justify-center">
              <span className="text-3xl">📸</span>
            </div>
            <div>
              <p className="text-white text-lg font-semibold mb-1">
                Drop images here or click to browse
              </p>
              <p className="text-gray-400 text-sm">
                Supports JPG, PNG, GIF, WebP (max 5MB)
              </p>
            </div>
          </div>
        </div>
      ) : (
        // Form for image details
        <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10">
          <div className="flex items-start space-x-6 mb-6">
            {/* Image Preview */}
            <div className="flex-shrink-0">
              <div className="w-32 h-32 rounded-lg overflow-hidden bg-gray-800">
                {previewUrl && (
                  <Image 
                    src={previewUrl} 
                    alt="Preview" 
                    width={128}
                    height={128}
                    className="w-full h-full object-cover"
                  />
                )}
              </div>
            </div>
            
            {/* Form */}
            <div className="flex-1">
              <h3 className="text-xl font-bold text-white mb-4">Tell us about your gaming moment!</h3>
              <form onSubmit={handleFormSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Your Name *
                    </label>
                    <input
                      type="text"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      placeholder="Enter your name"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Mobile Number *
                    </label>
                    <input
                      type="tel"
                      value={mobile}
                      onChange={(e) => setMobile(e.target.value)}
                      className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      placeholder="+1234567890"
                      required
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Description (Optional)
                  </label>
                  <textarea
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
                    placeholder="Share what makes this moment special..."
                    rows={3}
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Rate Your Experience *
                  </label>
                  {renderStarRating()}
                </div>
                
                <div className="flex space-x-4 pt-4">
                  <button
                    type="submit"
                    disabled={isUploading}
                    className="flex-1 bg-purple-500 hover:bg-purple-600 disabled:bg-purple-500/50 text-white font-bold py-3 px-6 rounded-lg transition-colors flex items-center justify-center space-x-2"
                  >
                    {isUploading ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        <span>Uploading...</span>
                      </>
                    ) : (
                      <>
                        <span>📤</span>
                        <span>Share Image</span>
                      </>
                    )}
                  </button>
                  
                  <button
                    type="button"
                    onClick={cancelUpload}
                    disabled={isUploading}
                    className="px-6 py-3 bg-gray-600 hover:bg-gray-500 disabled:bg-gray-600/50 text-white font-medium rounded-lg transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Status Messages */}
      {uploadError && (
        <div className="mt-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg">
          <p className="text-red-400 text-sm">{uploadError}</p>
        </div>
      )}

      {uploadSuccess && (
        <div className="mt-4 p-3 bg-green-500/20 border border-green-500/30 rounded-lg">
          <p className="text-green-400 text-sm">{uploadSuccess}</p>
        </div>
      )}
    </div>
  );
};

export default ImageUpload;