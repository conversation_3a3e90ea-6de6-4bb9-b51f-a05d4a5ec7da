import Link from 'next/link'

interface CommunityHeaderProps {
  title: string
  subtitle?: string
}

export default function CommunityHeader({ title, subtitle }: CommunityHeaderProps) {
  return (
    <header className="p-4 md:p-6">
      <div className="flex items-center justify-between">
        <Link href="/" className="flex items-center space-x-2 hover:opacity-80 transition-opacity">
          <span className="text-2xl">🎮</span>
          <span className="text-xl font-bold">VGzone</span>
        </Link>
        
        <Link
          href="/community"
          className="px-4 py-2 bg-primary-600/50 hover:bg-primary-600/70 rounded-lg transition-colors"
        >
          Community Home
        </Link>
      </div>
      
      <div className="text-center mt-8">
        <h1 className="text-4xl md:text-6xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
          {title}
        </h1>
        {subtitle && (
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            {subtitle}
          </p>
        )}
      </div>
    </header>
  )
}