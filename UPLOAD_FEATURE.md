# Image Upload Feature

This feature allows users to upload and share images in the VGzone community section.

## Features

- **Drag & Drop Upload**: Users can drag and drop images directly onto the upload area
- **Click to Browse**: Alternative method to select files through a file dialog
- **Image Validation**: 
  - Supports JPG, PNG, GIF, WebP formats
  - Maximum file size: 5MB
  - Client-side and server-side validation
- **Real-time Gallery**: Uploaded images appear immediately in the community gallery
- **Image Preview**: Click on any image in the gallery for a full-size view
- **Responsive Design**: Works on all device sizes

## File Structure

```
src/
├── app/
│   ├── api/
│   │   └── upload/
│   │       └── route.ts          # API endpoints for upload and image fetching
│   └── community/
│       └── page.tsx              # Community page with upload feature
├── components/
│   └── Community/
│       ├── ImageUpload.tsx       # Upload component with drag & drop
│       └── ImageGallery.tsx      # Gallery component with modal view
public/
└── uploads/                      # Directory for stored images
    └── .gitkeep                  # Ensures directory exists in git
```

## API Endpoints

### POST /api/upload
Upload a new image file.
- **Body**: FormData with 'file' field
- **Response**: `{ message, url, filename }` or `{ error }`

### GET /api/upload  
Retrieve list of uploaded images.
- **Response**: `{ images: [{ filename, url, uploadDate }] }`

## Storage

Images are stored locally in the `public/uploads/` directory with timestamped filenames to prevent conflicts. The directory is excluded from git commits for security and space reasons.

## Security Features

- File type validation (images only)
- File size limits (5MB maximum)  
- Filename sanitization
- Server-side validation

## Usage

1. Navigate to the Community page (`/community`)
2. Scroll down to the "Share Your Gaming Moments" section
3. Either drag & drop an image or click to browse
4. View uploaded images in the gallery below
5. Click any image for full-size preview