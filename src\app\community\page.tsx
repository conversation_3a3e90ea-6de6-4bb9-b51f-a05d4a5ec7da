"use client"

import React from 'react'
import Link from 'next/link'
import NavigationSlider from '@/components/Community/NavigationSlider'

const CommunityPage = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background-secondary via-background-tertiary to-background-primary text-foreground-primary">
      {/* Header with Back Button */}
      <header className="p-4 md:p-6">
        <div className="flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-2 hover:opacity-80 transition-opacity">
            <div className="w-8 h-8 bg-secondary-500 rounded flex items-center justify-center">
              <span className="text-foreground-primary font-bold">🎮</span>
            </div>
            <span className="text-xl font-bold">VGzone</span>
          </Link>
          <Link
            href="/"
            className="bg-background-card hover:bg-background-card-hover px-4 py-2 rounded-full transition-colors flex items-center space-x-2"
          >
            <span>←</span>
            <span>Back</span>
          </Link>
        </div>
      </header>

      {/* Community Content */}
      <div className="container mx-auto px-4 md:px-6 py-8 md:py-12">
        <div className="text-center mb-12">
          <NavigationSlider />
          <h1 className="text-4xl md:text-6xl font-bold mb-4">VGzone Community</h1>
          <p className="text-lg md:text-xl text-foreground-secondary max-w-2xl mx-auto">
            Connect with fellow gamers, join events, and be part of our vibrant gaming community!
          </p>
        </div>

        {/* Community Features - Direct Navigation Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto mb-12">
          <Link
            href="/community/memories"
            className="group bg-gradient-to-br from-primary-500/20 to-secondary-500/20 backdrop-blur-sm rounded-2xl p-8 hover:from-primary-500/30 hover:to-secondary-500/30 transition-all hover:scale-105 border border-primary-500/30"
          >
            <div className="text-5xl mb-4">📸</div>
            <h3 className="text-2xl font-bold mb-2">Gaming Memories</h3>
            <p className="text-foreground-secondary">Share your favorite gaming screenshots, setups, and epic moments with the community!</p>
            <div className="mt-4 text-primary-400 group-hover:text-primary-300 transition-colors">
              Explore Memories →
            </div>
          </Link>

          <Link
            href="/community/events"
            className="group bg-gradient-to-br from-info-500/20 to-primary-500/20 backdrop-blur-sm rounded-2xl p-8 hover:from-info-500/30 hover:to-primary-500/30 transition-all hover:scale-105 border border-info-500/30"
          >
            <div className="text-5xl mb-4">🎉</div>
            <h3 className="text-2xl font-bold mb-2">Gaming Events</h3>
            <p className="text-foreground-secondary">Join tournaments, game nights, and special community events happening at VGzone!</p>
            <div className="mt-4 text-info-400 group-hover:text-info-300 transition-colors">
              View Events →
            </div>
          </Link>

          <Link
            href="/community/leaderboard"
            className="group bg-gradient-to-br from-warning-500/20 to-warning-600/20 backdrop-blur-sm rounded-2xl p-8 hover:from-warning-500/30 hover:to-warning-600/30 transition-all hover:scale-105 border border-warning-500/30"
          >
            <div className="text-5xl mb-4">🏆</div>
            <h3 className="text-2xl font-bold mb-2">Leaderboards</h3>
            <p className="text-foreground-secondary">Check community rankings, tournament wins, and see who&apos;s dominating the competition!</p>
            <div className="mt-4 text-warning-400 group-hover:text-warning-300 transition-colors">
              View Rankings →
            </div>
          </Link>
        </div>

        {/* Quick Stats */}
        <div className="max-w-4xl mx-auto mb-16">
          <h2 className="text-3xl font-bold mb-6 text-center">Community at a Glance</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-background-card backdrop-blur-sm rounded-xl p-6 text-center border border-background-card-hover">
              <div className="text-3xl mb-2">👥</div>
              <div className="text-2xl font-bold">1,247</div>
              <div className="text-foreground-tertiary text-sm">Members</div>
            </div>
            <div className="bg-background-card backdrop-blur-sm rounded-xl p-6 text-center border border-background-card-hover">
              <div className="text-3xl mb-2">📸</div>
              <div className="text-2xl font-bold">2,156</div>
              <div className="text-foreground-tertiary text-sm">Memories</div>
            </div>
            <div className="bg-background-card backdrop-blur-sm rounded-xl p-6 text-center border border-background-card-hover">
              <div className="text-3xl mb-2">🎉</div>
              <div className="text-2xl font-bold">24</div>
              <div className="text-foreground-tertiary text-sm">Events</div>
            </div>
            <div className="bg-background-card backdrop-blur-sm rounded-xl p-6 text-center border border-background-card-hover">
              <div className="text-3xl mb-2">🏆</div>
              <div className="text-2xl font-bold">342</div>
              <div className="text-foreground-tertiary text-sm">Tournaments</div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <button className="bg-primary-500 hover:bg-primary-600 text-foreground-primary font-bold px-8 py-3 rounded-full transition-colors mr-4">
            Join Our Discord
          </button>
          <button className="bg-secondary-500 hover:bg-secondary-600 text-foreground-primary font-bold px-8 py-3 rounded-full transition-colors">
            View All Events
          </button>
        </div>
      </div>
    </div>
  )
}

export default CommunityPage
