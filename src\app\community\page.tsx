"use client"

import React from 'react'
import Link from 'next/link'
import NavigationSlider from '@/components/Community/NavigationSlider'

const CommunityPage = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-black text-white">
      {/* Header with Back Button */}
      <header className="p-4 md:p-6">
        <div className="flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-2 hover:opacity-80 transition-opacity">
            <div className="w-8 h-8 bg-pink-500 rounded flex items-center justify-center">
              <span className="text-white font-bold">🎮</span>
            </div>
            <span className="text-xl font-bold">VGzone</span>
          </Link>
          <Link 
            href="/" 
            className="bg-white/10 hover:bg-white/20 px-4 py-2 rounded-full transition-colors flex items-center space-x-2"
          >
            <span>←</span>
            <span>Back</span>
          </Link>
        </div>
      </header>

      {/* Community Content */}
      <div className="container mx-auto px-4 md:px-6 py-8 md:py-12">
        <div className="text-center mb-12">
          <NavigationSlider />
          <h1 className="text-4xl md:text-6xl font-bold mb-4">VGzone Community</h1>
          <p className="text-lg md:text-xl text-gray-300 max-w-2xl mx-auto">
            Connect with fellow gamers, join events, and be part of our vibrant gaming community!
          </p>
        </div>

        {/* Community Features - Direct Navigation Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto mb-12">
          <Link 
            href="/community/memories"
            className="group bg-gradient-to-br from-purple-500/20 to-pink-500/20 backdrop-blur-sm rounded-2xl p-8 hover:from-purple-500/30 hover:to-pink-500/30 transition-all hover:scale-105 border border-purple-500/30"
          >
            <div className="text-5xl mb-4">📸</div>
            <h3 className="text-2xl font-bold mb-2">Gaming Memories</h3>
            <p className="text-gray-300">Share your favorite gaming screenshots, setups, and epic moments with the community!</p>
            <div className="mt-4 text-purple-400 group-hover:text-purple-300 transition-colors">
              Explore Memories →
            </div>
          </Link>

          <Link 
            href="/community/events"
            className="group bg-gradient-to-br from-blue-500/20 to-purple-500/20 backdrop-blur-sm rounded-2xl p-8 hover:from-blue-500/30 hover:to-purple-500/30 transition-all hover:scale-105 border border-blue-500/30"
          >
            <div className="text-5xl mb-4">🎉</div>
            <h3 className="text-2xl font-bold mb-2">Gaming Events</h3>
            <p className="text-gray-300">Join tournaments, game nights, and special community events happening at VGzone!</p>
            <div className="mt-4 text-blue-400 group-hover:text-blue-300 transition-colors">
              View Events →
            </div>
          </Link>

          <Link 
            href="/community/leaderboard"
            className="group bg-gradient-to-br from-yellow-500/20 to-orange-500/20 backdrop-blur-sm rounded-2xl p-8 hover:from-yellow-500/30 hover:to-orange-500/30 transition-all hover:scale-105 border border-yellow-500/30"
          >
            <div className="text-5xl mb-4">🏆</div>
            <h3 className="text-2xl font-bold mb-2">Leaderboards</h3>
            <p className="text-gray-300">Check community rankings, tournament wins, and see who&apos;s dominating the competition!</p>
            <div className="mt-4 text-yellow-400 group-hover:text-yellow-300 transition-colors">
              View Rankings →
            </div>
          </Link>
        </div>

        {/* Quick Stats */}
        <div className="max-w-4xl mx-auto mb-16">
          <h2 className="text-3xl font-bold mb-6 text-center">Community at a Glance</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6 text-center border border-white/10">
              <div className="text-3xl mb-2">👥</div>
              <div className="text-2xl font-bold">1,247</div>
              <div className="text-gray-400 text-sm">Members</div>
            </div>
            <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6 text-center border border-white/10">
              <div className="text-3xl mb-2">📸</div>
              <div className="text-2xl font-bold">2,156</div>
              <div className="text-gray-400 text-sm">Memories</div>
            </div>
            <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6 text-center border border-white/10">
              <div className="text-3xl mb-2">🎉</div>
              <div className="text-2xl font-bold">24</div>
              <div className="text-gray-400 text-sm">Events</div>
            </div>
            <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6 text-center border border-white/10">
              <div className="text-3xl mb-2">🏆</div>
              <div className="text-2xl font-bold">342</div>
              <div className="text-gray-400 text-sm">Tournaments</div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <button className="bg-purple-500 hover:bg-purple-600 text-white font-bold px-8 py-3 rounded-full transition-colors mr-4">
            Join Our Discord
          </button>
          <button className="bg-pink-500 hover:bg-pink-600 text-white font-bold px-8 py-3 rounded-full transition-colors">
            View All Events
          </button>
        </div>
      </div>
    </div>
  )
}

export default CommunityPage
