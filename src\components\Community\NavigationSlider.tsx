"use client"

import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useRouter, usePathname } from 'next/navigation';

interface NavigationSliderProps {
  onSectionChange?: (section: string) => void;
}

const NavigationSlider: React.FC<NavigationSliderProps> = ({ 
  onSectionChange 
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isHolding, setIsHolding] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [translateX, setTranslateX] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const touchStartRef = useRef<number>(0);

  const sections = useMemo(() => [
    { id: 'memories', label: 'Memories', icon: '📸', color: 'from-purple-500 to-pink-500' },
    { id: 'events', label: 'Events', icon: '🎉', color: 'from-blue-500 to-purple-500' },
    { id: 'leaderboard', label: 'Leaderboard', icon: '🏆', color: 'from-yellow-500 to-orange-500' }
  ], []);

  // Initialize current section based on pathname
  useEffect(() => {
    const currentPath = pathname.split('/').pop();
    const sectionIndex = sections.findIndex(section => section.id === currentPath);
    if (sectionIndex !== -1) {
      setCurrentIndex(sectionIndex);
    } else {
      // Default to memories if on main community page
      setCurrentIndex(0);
    }
  }, [pathname, sections]);

  // Initialize with first section only if onSectionChange is provided (for backward compatibility)
  useEffect(() => {
    if (onSectionChange && !pathname.includes('/community/')) {
      onSectionChange(sections[0].id);
    }
  }, [onSectionChange, sections, pathname]);

  // Check if mobile on mount and resize
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Auto-slide functionality for mobile - continuous movement
  useEffect(() => {
    if (isMobile && !isHolding) {
      intervalRef.current = setInterval(() => {
        setTranslateX((prev) => {
          // Each button is approximately 180px wide (160px + 16px margin)
          const buttonWidth = 176;
          const totalWidth = sections.length * buttonWidth;
          
          // Move by 0.5px for smooth continuous motion
          const newTranslateX = prev - 0.5;
          
          // Reset position when we've moved a full cycle
          if (Math.abs(newTranslateX) >= totalWidth) {
            return 0;
          }
          
          return newTranslateX;
        });
      }, 16); // ~60fps for smooth animation
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isMobile, isHolding, sections.length]);

  // Navigate to section page instead of local state change
  const handleSectionClick = (index: number) => {
    const section = sections[index];
    setCurrentIndex(index);
    
    // Navigate to the section page
    router.push(`/community/${section.id}`);
    
    // Keep backward compatibility
    if (onSectionChange) {
      onSectionChange(section.id);
    }
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    touchStartRef.current = e.touches[0].clientX;
    setIsHolding(true);
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    const touchEnd = e.changedTouches[0].clientX;
    const diff = touchStartRef.current - touchEnd;
    
    // Swipe threshold - but don't change translateX, just pause/resume
    if (Math.abs(diff) > 50) {
      // Just visual feedback, no section change
    }
    
    setTimeout(() => setIsHolding(false), 100);
  };

  const handleMouseDown = () => {
    if (isMobile) {
      setIsHolding(true);
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }
  };

  const handleMouseUp = () => {
    if (isMobile) {
      setTimeout(() => setIsHolding(false), 100);
    }
  };

  if (isMobile) {
    // Mobile: Continuous sliding carousel showing all sections
    return (
      <div className="flex flex-col items-center mb-6">
        <div className="relative w-full max-w-sm overflow-hidden">
          <div 
            className="flex space-x-4 transition-transform duration-75 ease-linear p-2"
            style={{
              transform: `translateX(${translateX}px)`,
              width: `${sections.length * 200}px`, // Wider to accommodate all buttons
            }}
            onTouchStart={handleTouchStart}
            onTouchEnd={handleTouchEnd}
            onMouseDown={handleMouseDown}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
          >
            {/* Render sections multiple times for seamless loop */}
            {[...sections, ...sections, ...sections].map((section, index) => (
              <button
                key={`${section.id}-${index}`}
                onClick={() => handleSectionClick(sections.findIndex(s => s.id === section.id))}
                className={`flex-shrink-0 inline-flex items-center bg-gradient-to-r ${section.color} text-white px-6 py-1 rounded-full text-sm font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg ${
                  currentIndex === sections.findIndex(s => s.id === section.id) 
                    ? 'ring-2 ring-white/50 scale-105' 
                    : 'opacity-80 hover:opacity-100'
                }`}
                style={{
                  transform: isHolding ? 'scale(0.95)' : 'scale(1)',
                  minWidth: '160px'
                }}
              >
                <span className="mr-2 text-lg">{section.icon}</span>
                <span className="font-bold">{section.label}</span>
              </button>
            ))}
          </div>
        </div>
        
        {/* Dots indicator */}
        <div className="flex space-x-2 mt-3">
          {sections.map((_, index) => (
            <button
              key={index}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${
                index === currentIndex 
                  ? 'bg-white scale-125' 
                  : 'bg-white/40 hover:bg-white/60'
              }`}
              onClick={() => handleSectionClick(index)}
            />
          ))}
        </div>
        
        {/* Hold instruction */}
        <p className="text-xs text-gray-400 mt-2 text-center">
          Hold to pause sliding • Tap sections to navigate
        </p>
      </div>
    );
  }

  // Desktop: All three items side by side
  return (
    <div className="flex flex-wrap justify-center gap-4 mb-6">
      {sections.map((section, index) => (
        <button
          key={section.id}
          onClick={() => handleSectionClick(index)}
          className={`inline-flex items-center bg-gradient-to-r ${section.color} text-white px-6 py-3 rounded-full text-sm font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg ${
            currentIndex === index 
              ? 'ring-2 ring-white/50 scale-105' 
              : 'opacity-80 hover:opacity-100'
          }`}
        >
          <span className="mr-2 text-lg">{section.icon}</span>
          <span className="font-bold">{section.label}</span>
        </button>
      ))}
    </div>
  );
};

export default NavigationSlider;