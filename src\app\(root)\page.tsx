"use client"

import React, { useState } from 'react'
import Header from '@/components/MainPage/Header'
import HeroSection from '@/components/MainPage/HeroSection'
import GameCarousel from '@/components/MainPage/GameCarousel'
import ColorTest from '@/components/ColorTest'
import { GAME_CARDS } from '@/data/gameData'

const Page = () => {
  const [searchQuery, setSearchQuery] = useState('')

  return (
    <div className="min-h-screen bg-gradient-to-br from-background-secondary via-background-tertiary to-background-primary text-foreground-primary">
      <Header searchQuery={searchQuery} onSearchChange={setSearchQuery} />
      <ColorTest />
      <HeroSection />
      <GameCarousel games={GAME_CARDS} searchQuery={searchQuery} />
    </div>
  )
}

export default Page