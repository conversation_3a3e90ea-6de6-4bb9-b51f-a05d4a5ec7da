"use client"

import React, { useState } from 'react'
import Header from '@/components/MainPage/Header'
import HeroSection from '@/components/MainPage/HeroSection'
import GameCarousel from '@/components/MainPage/GameCarousel'
import { GAME_CARDS } from '@/data/gameData'

const Page = () => {
  const [searchQuery, setSearchQuery] = useState('')

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-black text-white">
      <Header searchQuery={searchQuery} onSearchChange={setSearchQuery} />
      <HeroSection />
      <GameCarousel games={GAME_CARDS} searchQuery={searchQuery} />
    </div>
  )
}

export default Page