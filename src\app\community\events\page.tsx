"use client"

import React from 'react'
import Link from 'next/link'
import NavigationSlider from '@/components/Community/NavigationSlider'

const EventsPage = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-black text-white">
      {/* Header with Back Button */}
      <header className="p-4 md:p-6">
        <div className="flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-2 hover:opacity-80 transition-opacity">
            <div className="w-8 h-8 bg-pink-500 rounded flex items-center justify-center">
              <span className="text-white font-bold">🎮</span>
            </div>
            <span className="text-xl font-bold">VGzone</span>
          </Link>
          <Link 
            href="/community" 
            className="bg-white/10 hover:bg-white/20 px-4 py-2 rounded-full transition-colors flex items-center space-x-2"
          >
            <span>←</span>
            <span>Back</span>
          </Link>
        </div>
      </header>

      {/* Community Content */}
      <div className="container mx-auto px-4 md:px-6 py-8 md:py-12">
        <div className="text-center mb-12">
          <NavigationSlider />
          <h1 className="text-4xl md:text-6xl font-bold mb-4">Gaming Events</h1>
          <p className="text-lg md:text-xl text-gray-300 max-w-2xl mx-auto">
            Join exciting tournaments, community nights, and special gaming events!
          </p>
        </div>

        {/* Upcoming Events */}
        <div className="max-w-4xl mx-auto mb-16">
          <h2 className="text-3xl font-bold mb-6 text-center">Upcoming Events</h2>
          <div className="space-y-6">
            <div className="bg-gradient-to-r from-pink-500/20 to-purple-500/20 backdrop-blur-sm rounded-xl p-6 border border-pink-500/30">
              <div className="flex items-start justify-between flex-wrap gap-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <span className="text-2xl">🥊</span>
                    <h3 className="text-2xl font-bold">Friday Night Fighters</h3>
                  </div>
                  <p className="text-gray-300 mb-3">Fighting game tournament - All skill levels welcome! Featuring Street Fighter, Tekken, and Mortal Kombat.</p>
                  <div className="flex flex-wrap gap-2 mb-3">
                    <span className="bg-pink-500/30 px-3 py-1 rounded-full text-sm">Tournament</span>
                    <span className="bg-purple-500/30 px-3 py-1 rounded-full text-sm">Prizes</span>
                    <span className="bg-blue-500/30 px-3 py-1 rounded-full text-sm">All Levels</span>
                  </div>
                  <p className="text-sm text-gray-400">Entry Fee: $10 • Prize Pool: $500</p>
                </div>
                <div className="text-right">
                  <div className="text-pink-400 font-bold text-xl">This Friday</div>
                  <div className="text-gray-400">7:00 PM - 11:00 PM</div>
                  <button className="mt-2 bg-pink-500 hover:bg-pink-600 px-4 py-2 rounded-lg transition-colors">
                    Register Now
                  </button>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-sm rounded-xl p-6 border border-blue-500/30">
              <div className="flex items-start justify-between flex-wrap gap-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <span className="text-2xl">🕹️</span>
                    <h3 className="text-2xl font-bold">Retro Gaming Night</h3>
                  </div>
                  <p className="text-gray-300 mb-3">Nostalgic classics on original consoles! Pac-Man, Donkey Kong, Super Mario Bros, and more retro favorites.</p>
                  <div className="flex flex-wrap gap-2 mb-3">
                    <span className="bg-blue-500/30 px-3 py-1 rounded-full text-sm">Retro</span>
                    <span className="bg-yellow-500/30 px-3 py-1 rounded-full text-sm">Casual</span>
                    <span className="bg-green-500/30 px-3 py-1 rounded-full text-sm">Family</span>
                  </div>
                  <p className="text-sm text-gray-400">Free Entry • Food & Drinks Available</p>
                </div>
                <div className="text-right">
                  <div className="text-blue-400 font-bold text-xl">Next Saturday</div>
                  <div className="text-gray-400">2:00 PM - 8:00 PM</div>
                  <button className="mt-2 bg-blue-500 hover:bg-blue-600 px-4 py-2 rounded-lg transition-colors">
                    Join Event
                  </button>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-sm rounded-xl p-6 border border-yellow-500/30">
              <div className="flex items-start justify-between flex-wrap gap-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <span className="text-2xl">🎉</span>
                    <h3 className="text-2xl font-bold">Community Game Night</h3>
                  </div>
                  <p className="text-gray-300 mb-3">Party games, food specials, and great vibes! Among Us, Fall Guys, Mario Kart, and board games too!</p>
                  <div className="flex flex-wrap gap-2 mb-3">
                    <span className="bg-yellow-500/30 px-3 py-1 rounded-full text-sm">Weekly</span>
                    <span className="bg-orange-500/30 px-3 py-1 rounded-full text-sm">Social</span>
                    <span className="bg-red-500/30 px-3 py-1 rounded-full text-sm">Party Games</span>
                  </div>
                  <p className="text-sm text-gray-400">Free Entry • 25% off food during event</p>
                </div>
                <div className="text-right">
                  <div className="text-yellow-400 font-bold text-xl">Every Wednesday</div>
                  <div className="text-gray-400">6:00 PM - 10:00 PM</div>
                  <button className="mt-2 bg-yellow-500 hover:bg-yellow-600 px-4 py-2 rounded-lg transition-colors text-black font-semibold">
                    Join Tonight
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Event Categories */}
        <div className="max-w-4xl mx-auto mb-16">
          <h2 className="text-3xl font-bold mb-6 text-center">Event Categories</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 hover:bg-white/20 transition-all hover:scale-105">
              <div className="text-4xl mb-4">🏆</div>
              <h3 className="text-xl font-bold mb-2">Tournaments</h3>
              <p className="text-gray-300 text-sm">Competitive events with prizes and glory on the line!</p>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 hover:bg-white/20 transition-all hover:scale-105">
              <div className="text-4xl mb-4">🎮</div>
              <h3 className="text-xl font-bold mb-2">Game Nights</h3>
              <p className="text-gray-300 text-sm">Casual gaming sessions with friends and community!</p>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 hover:bg-white/20 transition-all hover:scale-105">
              <div className="text-4xl mb-4">🎓</div>
              <h3 className="text-xl font-bold mb-2">Workshops</h3>
              <p className="text-gray-300 text-sm">Learn new strategies, techniques, and gaming skills!</p>
            </div>
          </div>
        </div>

        {/* Past Events */}
        <div className="max-w-4xl mx-auto mb-16">
          <h2 className="text-3xl font-bold mb-6 text-center">Recent Past Events</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-white/5 backdrop-blur-sm rounded-xl p-4 border border-white/10">
              <h4 className="font-bold mb-1">VGzone Championship 2024</h4>
              <p className="text-gray-400 text-sm mb-2">Multi-game tournament with 50+ participants</p>
              <span className="text-green-400 text-xs">✓ Completed</span>
            </div>
            <div className="bg-white/5 backdrop-blur-sm rounded-xl p-4 border border-white/10">
              <h4 className="font-bold mb-1">Indie Game Showcase</h4>
              <p className="text-gray-400 text-sm mb-2">Featured 10 upcoming indie titles</p>
              <span className="text-green-400 text-xs">✓ Completed</span>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <button className="bg-purple-500 hover:bg-purple-600 text-white font-bold px-8 py-3 rounded-full transition-colors mr-4">
            Create Event
          </button>
          <button className="bg-pink-500 hover:bg-pink-600 text-white font-bold px-8 py-3 rounded-full transition-colors">
            View Calendar
          </button>
        </div>
      </div>
    </div>
  )
}

export default EventsPage