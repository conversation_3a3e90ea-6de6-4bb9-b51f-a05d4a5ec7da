import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        // Primary brand colors
        primary: {
          50: '#faf5ff',
          100: '#f3e8ff',
          200: '#e9d5ff',
          300: '#d8b4fe',
          400: '#c084fc',
          500: '#a855f7', // Main purple
          600: '#9333ea',
          700: '#7c3aed',
          800: '#6b21a8',
          900: '#581c87',
          950: '#3b0764',
        },
        secondary: {
          50: '#fdf2f8',
          100: '#fce7f3',
          200: '#fbcfe8',
          300: '#f9a8d4',
          400: '#f472b6',
          500: '#ec4899', // Main pink
          600: '#db2777',
          700: '#be185d',
          800: '#9d174d',
          900: '#831843',
          950: '#500724',
        },
        // Background colors (flattened for Tailwind)
        'bg-primary': '#000000', // Black
        'bg-secondary': '#581c87', // Purple-900
        'bg-tertiary': '#6b21a8', // Purple-800
        'bg-card': 'rgba(255, 255, 255, 0.05)', // white/5
        'bg-card-hover': 'rgba(255, 255, 255, 0.1)', // white/10
        'bg-card-active': 'rgba(255, 255, 255, 0.2)', // white/20
        'bg-overlay': 'rgba(0, 0, 0, 0.8)', // black/80
        // Text colors (flattened for Tailwind)
        'text-primary': '#ffffff', // White
        'text-secondary': '#d1d5db', // Gray-300
        'text-tertiary': '#9ca3af', // Gray-400
        'text-muted': '#6b7280', // Gray-500
        // Accent colors for game cards (flattened for gradients)
        'accent-warm-pink-from': '#f472b6', // pink-400
        'accent-warm-pink-to': '#fbbf24', // yellow-400
        'accent-warm-red-from': '#ef4444', // red-500
        'accent-warm-red-to': '#7c3aed', // purple-700
        'accent-warm-orange-from': '#f97316', // orange-600
        'accent-warm-orange-to': '#dc2626', // red-700
        'accent-warm-yellow-from': '#fbbf24', // yellow-400
        'accent-warm-yellow-to': '#f97316', // orange-500
        'accent-cool-blue-from': '#2563eb', // blue-600
        'accent-cool-blue-to': '#6b21a8', // purple-800
        'accent-cool-teal-from': '#14b8a6', // teal-500
        'accent-cool-teal-to': '#1d4ed8', // blue-700
        'accent-cool-indigo-from': '#818cf8', // indigo-400
        'accent-cool-indigo-to': '#9333ea', // purple-600
        'accent-cool-cyan-from': '#06b6d4', // cyan-400
        'accent-cool-cyan-to': '#9333ea', // purple-600
        'accent-nature-green-from': '#10b981', // green-500
        'accent-nature-green-to': '#059669', // green-700
        'accent-nature-lime-from': '#84cc16', // lime-400
        'accent-nature-lime-to': '#16a34a', // green-600
        'accent-nature-emerald-from': '#10b981', // green-400
        'accent-nature-emerald-to': '#0d9488', // teal-600
        'accent-neutral-gray-from': '#6b7280', // gray-600
        'accent-neutral-gray-to': '#374151', // gray-800
        'accent-neutral-amber-from': '#d97706', // amber-600
        'accent-neutral-amber-to': '#92400e', // brown-700
        // Status colors
        success: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#22c55e',
          600: '#16a34a',
          700: '#15803d',
          800: '#166534',
          900: '#14532d',
        },
        error: {
          50: '#fef2f2',
          100: '#fee2e2',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#ef4444',
          600: '#dc2626',
          700: '#b91c1c',
          800: '#991b1b',
          900: '#7f1d1d',
        },
        warning: {
          50: '#fffbeb',
          100: '#fef3c7',
          200: '#fde68a',
          300: '#fcd34d',
          400: '#fbbf24',
          500: '#f59e0b',
          600: '#d97706',
          700: '#b45309',
          800: '#92400e',
          900: '#78350f',
        },
        info: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        },
      },
    },
  },
  plugins: [],
};

export default config;