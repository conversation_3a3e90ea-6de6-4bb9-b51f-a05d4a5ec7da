"use client"

import React, { useState, useEffect } from 'react';
import Image from 'next/image';

interface UploadedImage {
  filename: string;
  url: string;
  uploadDate: string;
  name?: string;
  mobile?: string;
  description?: string;
  rating?: number;
  originalName?: string;
}

interface ImageGalleryProps {
  refreshTrigger?: number;
}

const ImageGallery: React.FC<ImageGalleryProps> = ({ refreshTrigger }) => {
  const [images, setImages] = useState<UploadedImage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedImage, setSelectedImage] = useState<UploadedImage | null>(null);

  const fetchImages = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/upload');
      const data = await response.json();

      if (response.ok) {
        setImages(data.images || []);
        setError(null);
      } else {
        setError(data.error || 'Failed to fetch images');
      }
    } catch (err) {
      setError('Failed to fetch images');
      console.error('Fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchImages();
  }, [refreshTrigger]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const renderStars = (rating?: number) => {
    if (!rating) return null;
    
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <span
            key={star}
            className={`text-sm ${
              star <= rating ? 'text-yellow-400' : 'text-gray-600'
            }`}
          >
            ★
          </span>
        ))}
        <span className="text-xs text-gray-400 ml-1">({rating})</span>
      </div>
    );
  };

  const openModal = (image: UploadedImage) => {
    setSelectedImage(image);
  };

  const closeModal = () => {
    setSelectedImage(null);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="w-8 h-8 border-4 border-purple-400 border-t-transparent rounded-full animate-spin"></div>
        <span className="ml-3 text-white">Loading images...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-500/20 border border-red-500/30 rounded-lg">
        <p className="text-red-400">{error}</p>
      </div>
    );
  }

  if (images.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="w-16 h-16 bg-gray-600/20 rounded-full flex items-center justify-center mx-auto mb-4">
          <span className="text-3xl">🖼️</span>
        </div>
        <p className="text-gray-400">No images uploaded yet</p>
        <p className="text-gray-500 text-sm">Upload your first image to get started!</p>
      </div>
    );
  }

  return (
    <div>
      <h3 className="text-2xl font-bold text-white mb-6">Community Gallery ({images.length})</h3>
      
      {/* Image Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {images.map((image) => (
          <div 
            key={image.filename}
            className="group relative bg-white/10 rounded-lg overflow-hidden hover:bg-white/20 transition-all cursor-pointer"
            onClick={() => openModal(image)}
          >
            <div className="aspect-square relative">
              <Image
                src={image.url}
                alt={image.filename}
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-300"
                sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
              />
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors"></div>
            </div>
            
            <div className="p-3">
              <div className="flex items-start justify-between mb-2">
                <div className="flex-1 min-w-0">
                  <p className="text-white text-sm font-medium truncate">
                    {image.name || 'Anonymous'}
                  </p>
                  {image.rating && renderStars(image.rating)}
                </div>
              </div>
              
              {image.description && (
                <p className="text-gray-300 text-xs mb-2 line-clamp-2">
                  {image.description}
                </p>
              )}
              
              <p className="text-gray-400 text-xs">{formatDate(image.uploadDate)}</p>
            </div>
          </div>
        ))}
      </div>

      {/* Modal for full-size image view */}
      {selectedImage && (
        <div 
          className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={closeModal}
        >
          <div className="relative max-w-4xl max-h-full">
            <button
              onClick={closeModal}
              className="absolute -top-12 right-0 text-white hover:text-gray-300 transition-colors z-10"
            >
              <span className="text-2xl">✕</span>
            </button>
            
            <div className="relative">
              <Image
                src={selectedImage.url}
                alt={selectedImage.filename}
                width={800}
                height={600}
                className="max-w-full max-h-[80vh] object-contain rounded-lg"
                onClick={(e) => e.stopPropagation()}
              />
              
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 via-black/70 to-transparent p-6 rounded-b-lg">
                <div className="space-y-2">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <p className="text-white font-semibold text-lg">
                        {selectedImage.name || 'Anonymous'}
                      </p>
                      {selectedImage.rating && (
                        <div className="mt-1">
                          {renderStars(selectedImage.rating)}
                        </div>
                      )}
                    </div>
                    <p className="text-gray-300 text-sm">{formatDate(selectedImage.uploadDate)}</p>
                  </div>
                  
                  {selectedImage.description && (
                    <p className="text-gray-200 text-sm leading-relaxed">
                      {selectedImage.description}
                    </p>
                  )}
                  
                  <p className="text-gray-400 text-xs">
                    Filename: {selectedImage.originalName || selectedImage.filename}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageGallery;