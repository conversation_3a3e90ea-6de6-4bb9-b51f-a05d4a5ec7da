"use client"

import React, { useState } from 'react'
import Link from 'next/link'
import NavigationSlider from '@/components/Community/NavigationSlider'

const LeaderboardPage = () => {
  const [selectedCategory, setSelectedCategory] = useState('overall');

  const categories = [
    { id: 'overall', name: 'Overall', icon: '🏆' },
    { id: 'weekly', name: 'Weekly', icon: '📅' },
    { id: 'tournaments', name: 'Tournaments', icon: '🥇' },
    { id: 'uploads', name: 'Uploads', icon: '📸' }
  ];

  const leaderboardData = {
    overall: [
      { rank: 1, name: "GameMaster2024", xp: 15240, avatar: "🥇", badge: "Legend" },
      { rank: 2, name: "ProGamer99", xp: 12890, avatar: "🥈", badge: "Master" },
      { rank: 3, name: "ElitePlayer", xp: 11560, avatar: "🥉", badge: "Expert" },
      { rank: 4, name: "<PERSON>berNinja", xp: 9840, avatar: "⚡", badge: "Pro" },
      { rank: 5, name: "PixelWarrior", xp: 8760, avatar: "🛡️", badge: "Pro" },
      { rank: 6, name: "RetroGamer", xp: 7650, avatar: "🕹️", badge: "Advanced" },
      { rank: 7, name: "SpeedRunner", xp: 6890, avatar: "🏃", badge: "Advanced" },
      { rank: 8, name: "QuestMaster", xp: 6120, avatar: "🗡️", badge: "Intermediate" },
      { rank: 9, name: "ArcadeKing", xp: 5540, avatar: "👑", badge: "Intermediate" },
      { rank: 10, name: "NoobSlayer", xp: 4980, avatar: "⚔️", badge: "Novice" }
    ],
    weekly: [
      { rank: 1, name: "SpeedRunner", xp: 2890, avatar: "🏃", badge: "Weekly Champion" },
      { rank: 2, name: "NewRookie", xp: 2340, avatar: "🌟", badge: "Rising Star" },
      { rank: 3, name: "CyberNinja", xp: 2120, avatar: "⚡", badge: "Consistent" }
    ],
    tournaments: [
      { rank: 1, name: "ProGamer99", wins: 12, avatar: "🥈", badge: "Tournament King" },
      { rank: 2, name: "ElitePlayer", wins: 8, avatar: "🥉", badge: "Champion" },
      { rank: 3, name: "GameMaster2024", wins: 6, avatar: "🥇", badge: "Victor" }
    ],
    uploads: [
      { rank: 1, name: "PixelWarrior", uploads: 45, avatar: "🛡️", badge: "Content Creator" },
      { rank: 2, name: "RetroGamer", uploads: 32, avatar: "🕹️", badge: "Sharer" },
      { rank: 3, name: "ArcadeKing", uploads: 28, avatar: "👑", badge: "Active" }
    ]
  };

  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1: return 'from-yellow-500 to-orange-500';
      case 2: return 'from-gray-300 to-gray-500';
      case 3: return 'from-orange-600 to-red-500';
      default: return 'from-purple-500 to-blue-500';
    }
  };

  const getStatLabel = (category: string) => {
    switch (category) {
      case 'tournaments': return 'Wins';
      case 'uploads': return 'Uploads';
      default: return 'XP';
    }
  };

  const getStatValue = (player: { xp?: number; wins?: number; uploads?: number }, category: string) => {
    switch (category) {
      case 'tournaments': return player.wins;
      case 'uploads': return player.uploads;
      default: return player.xp?.toLocaleString();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-black text-white">
      {/* Header with Back Button */}
      <header className="p-4 md:p-6">
        <div className="flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-2 hover:opacity-80 transition-opacity">
            <div className="w-8 h-8 bg-pink-500 rounded flex items-center justify-center">
              <span className="text-white font-bold">🎮</span>
            </div>
            <span className="text-xl font-bold">VGzone</span>
          </Link>
          <Link 
            href="/community" 
            className="bg-white/10 hover:bg-white/20 px-4 py-2 rounded-full transition-colors flex items-center space-x-2"
          >
            <span>←</span>
            <span>Back</span>
          </Link>
        </div>
      </header>

      {/* Community Content */}
      <div className="container mx-auto px-4 md:px-6 py-8 md:py-12">
        <div className="text-center mb-12">
          <NavigationSlider />
          <h1 className="text-4xl md:text-6xl font-bold mb-4">Community Leaderboard</h1>
          <p className="text-lg md:text-xl text-gray-300 max-w-2xl mx-auto">
            See who&apos;s dominating the VGzone community across different categories!
          </p>
        </div>

        {/* Category Selector */}
        <div className="max-w-4xl mx-auto mb-8">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`inline-flex items-center px-6 py-3 rounded-full text-sm font-semibold transition-all duration-300 ${
                  selectedCategory === category.id
                    ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white scale-105 ring-2 ring-white/50'
                    : 'bg-white/10 text-gray-300 hover:bg-white/20 hover:text-white'
                }`}
              >
                <span className="mr-2 text-lg">{category.icon}</span>
                <span>{category.name}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Top 3 Podium */}
        <div className="max-w-4xl mx-auto mb-12">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            {leaderboardData[selectedCategory as keyof typeof leaderboardData].slice(0, 3).map((player, index) => (
              <div key={player.rank} className={`bg-gradient-to-br ${getRankColor(player.rank)}/20 rounded-xl p-6 text-center border ${getRankColor(player.rank).includes('yellow') ? 'border-yellow-500/30' : getRankColor(player.rank).includes('gray') ? 'border-gray-400/30' : 'border-orange-500/30'}`}>
                <div className="text-6xl mb-4">{player.avatar}</div>
                <div className="text-4xl mb-2">
                  {index === 0 ? '🥇' : index === 1 ? '🥈' : '🥉'}
                </div>
                <h3 className="text-xl font-bold mb-2">{player.name}</h3>
                <div className="text-2xl font-bold mb-1">
                  {getStatValue(player, selectedCategory)} {getStatLabel(selectedCategory)}
                </div>
                <span className={`inline-block px-3 py-1 rounded-full text-xs font-semibold ${
                  index === 0 ? 'bg-yellow-500/30 text-yellow-300' : 
                  index === 1 ? 'bg-gray-400/30 text-gray-300' : 
                  'bg-orange-500/30 text-orange-300'
                }`}>
                  {player.badge}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Full Leaderboard */}
        <div className="max-w-4xl mx-auto mb-16">
          <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 border border-white/10">
            <h2 className="text-2xl font-bold mb-6 text-center">
              {categories.find(c => c.id === selectedCategory)?.name} Rankings
            </h2>
            
            <div className="space-y-2">
              {leaderboardData[selectedCategory as keyof typeof leaderboardData].map((player, index) => (
                <div 
                  key={`${player.rank}-${player.name}`} 
                  className={`flex items-center justify-between rounded-lg p-4 transition-all hover:scale-[1.02] ${
                    index < 3 
                      ? `bg-gradient-to-r ${getRankColor(player.rank)}/10 border border-white/20` 
                      : 'bg-white/5 hover:bg-white/10'
                  }`}
                >
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-3">
                      <span className={`text-2xl font-bold ${
                        index === 0 ? 'text-yellow-400' : 
                        index === 1 ? 'text-gray-300' : 
                        index === 2 ? 'text-orange-400' : 
                        'text-purple-400'
                      }`}>
                        #{player.rank}
                      </span>
                      <span className="text-2xl">{player.avatar}</span>
                    </div>
                    <div>
                      <span className="text-white font-semibold text-lg">{player.name}</span>
                      <div className="text-sm text-gray-400">{player.badge}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-xl font-bold text-white">
                      {getStatValue(player, selectedCategory)}
                    </div>
                    <div className="text-sm text-gray-400">{getStatLabel(selectedCategory)}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Leaderboard Stats */}
        <div className="max-w-4xl mx-auto mb-16">
          <h2 className="text-3xl font-bold mb-6 text-center">Community Stats</h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 text-center">
              <div className="text-3xl mb-2">👥</div>
              <div className="text-2xl font-bold">1,247</div>
              <div className="text-gray-400 text-sm">Active Players</div>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 text-center">
              <div className="text-3xl mb-2">🎮</div>
              <div className="text-2xl font-bold">15,892</div>
              <div className="text-gray-400 text-sm">Games Played</div>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 text-center">
              <div className="text-3xl mb-2">🏆</div>
              <div className="text-2xl font-bold">342</div>
              <div className="text-gray-400 text-sm">Tournaments</div>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 text-center">
              <div className="text-3xl mb-2">📸</div>
              <div className="text-2xl font-bold">2,156</div>
              <div className="text-gray-400 text-sm">Memories Shared</div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <button className="bg-purple-500 hover:bg-purple-600 text-white font-bold px-8 py-3 rounded-full transition-colors mr-4">
            Climb Rankings
          </button>
          <button className="bg-pink-500 hover:bg-pink-600 text-white font-bold px-8 py-3 rounded-full transition-colors">
            View My Stats
          </button>
        </div>
      </div>
    </div>
  )
}

export default LeaderboardPage