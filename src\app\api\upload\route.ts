import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir, readdir, stat, readFile } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const name = formData.get('name') as string;
    const mobile = formData.get('mobile') as string;
    const description = formData.get('description') as string || '';
    const rating = formData.get('rating') as string;

    if (!file) {
      return NextResponse.json({ error: 'No file uploaded' }, { status: 400 });
    }

    // Validate required fields
    if (!name || !mobile || !rating) {
      return NextResponse.json({ 
        error: 'Name, mobile number, and rating are required' 
      }, { status: 400 });
    }

    // Validate mobile number format (basic validation)
    const mobileRegex = /^[+]?[0-9]{10,15}$/;
    if (!mobileRegex.test(mobile.replace(/\s/g, ''))) {
      return NextResponse.json({ 
        error: 'Please enter a valid mobile number' 
      }, { status: 400 });
    }

    // Validate rating
    const ratingNum = parseInt(rating);
    if (isNaN(ratingNum) || ratingNum < 1 || ratingNum > 5) {
      return NextResponse.json({ 
        error: 'Rating must be between 1 and 5' 
      }, { status: 400 });
    }

    // Validate file type (only images)
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({ 
        error: 'Invalid file type. Only images are allowed.' 
      }, { status: 400 });
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return NextResponse.json({ 
        error: 'File too large. Maximum size is 5MB.' 
      }, { status: 400 });
    }

    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // Create uploads directory if it doesn't exist
    const uploadsDir = path.join(process.cwd(), 'public', 'uploads');
    if (!existsSync(uploadsDir)) {
      await mkdir(uploadsDir, { recursive: true });
    }

    // Generate unique filename
    const timestamp = Date.now();
    const originalName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
    const filename = `${timestamp}_${originalName}`;
    const filepath = path.join(uploadsDir, filename);

    // Write the file
    await writeFile(filepath, buffer);

    // Create metadata file
    const metadataFilename = `${timestamp}_${originalName}.json`;
    const metadataPath = path.join(uploadsDir, metadataFilename);
    const metadata = {
      filename: filename,
      originalName: file.name,
      name: name,
      mobile: mobile,
      description: description,
      rating: ratingNum,
      uploadDate: new Date().toISOString(),
      fileSize: file.size,
      mimeType: file.type
    };
    
    await writeFile(metadataPath, JSON.stringify(metadata, null, 2));

    // Return the public URL
    const publicUrl = `/uploads/${filename}`;

    return NextResponse.json({ 
      message: 'Image uploaded successfully with your information!',
      url: publicUrl,
      filename: filename,
      metadata: metadata
    });

  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json({ 
      error: 'Failed to upload file' 
    }, { status: 500 });
  }
}

// Get uploaded images
export async function GET() {
  try {
    const uploadsDir = path.join(process.cwd(), 'public', 'uploads');
    
    if (!existsSync(uploadsDir)) {
      return NextResponse.json({ images: [] });
    }

    const files = await readdir(uploadsDir);
    
    interface ImageFile {
      filename: string;
      url: string;
      uploadDate: Date;
      name?: string;
      mobile?: string;
      description?: string;
      rating?: number;
      originalName?: string;
    }
    
    const images: ImageFile[] = [];
    
    for (const file of files) {
      const ext = path.extname(file).toLowerCase();
      if (['.jpg', '.jpeg', '.png', '.gif', '.webp'].includes(ext)) {
        const filePath = path.join(uploadsDir, file);
        const stats = await stat(filePath);
        
        // Try to read metadata
        const metadataPath = path.join(uploadsDir, `${file}.json`);
        let metadata = {};
        try {
          const metadataContent = await readFile(metadataPath, 'utf-8');
          metadata = JSON.parse(metadataContent);
        } catch {
          // Metadata file doesn't exist or is invalid, continue without it
        }
        
        images.push({
          filename: file,
          url: `/uploads/${file}`,
          uploadDate: stats.mtime,
          ...metadata
        });
      }
    }
    
    // Sort by upload date (newest first)
    images.sort((a, b) => new Date(b.uploadDate).getTime() - new Date(a.uploadDate).getTime());

    return NextResponse.json({ images });

  } catch (error) {
    console.error('Error fetching images:', error);
    return NextResponse.json({ 
      error: 'Failed to fetch images' 
    }, { status: 500 });
  }
}