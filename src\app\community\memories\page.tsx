"use client"

import React, { useState } from 'react'
import Link from 'next/link'
import ImageUpload from '@/components/Community/ImageUpload'
import ImageGallery from '@/components/Community/ImageGallery'
import NavigationSlider from '@/components/Community/NavigationSlider'

const MemoriesPage = () => {
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleImageUploaded = () => {
    // Trigger gallery refresh when new image is uploaded
    setRefreshTrigger(prev => prev + 1);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-black text-white">
      {/* Header with Back Button */}
      <header className="p-4 md:p-6">
        <div className="flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-2 hover:opacity-80 transition-opacity">
            <div className="w-8 h-8 bg-pink-500 rounded flex items-center justify-center">
              <span className="text-white font-bold">🎮</span>
            </div>
            <span className="text-xl font-bold">VGzone</span>
          </Link>
          <Link 
            href="/community" 
            className="bg-white/10 hover:bg-white/20 px-4 py-2 rounded-full transition-colors flex items-center space-x-2"
          >
            <span>←</span>
            <span>Back</span>
          </Link>
        </div>
      </header>

      {/* Community Content */}
      <div className="container mx-auto px-4 md:px-6 py-8 md:py-12">
        <div className="text-center mb-12">
          <NavigationSlider />
          <h1 className="text-4xl md:text-6xl font-bold mb-4">Gaming Memories</h1>
          <p className="text-lg md:text-xl text-gray-300 max-w-2xl mx-auto">
            Share your favorite gaming moments, screenshots, and memories with the community!
          </p>
        </div>

        {/* Image Upload Section */}
        <div className="max-w-4xl mx-auto mb-16">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold mb-4">Share Your Gaming Moments</h2>
            <p className="text-gray-300">Upload and share your favorite gaming screenshots, setups, and memories with the community!</p>
          </div>
          
          <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 border border-white/10">
            <ImageUpload onImageUploaded={handleImageUploaded} />
          </div>
        </div>

        {/* Image Gallery Section */}
        <div className="max-w-6xl mx-auto mb-16">
          <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 border border-white/10">
            <ImageGallery refreshTrigger={refreshTrigger} />
          </div>
        </div>

        {/* Featured Memories */}
        <div className="max-w-4xl mx-auto mb-16">
          <h2 className="text-3xl font-bold mb-6 text-center">Featured Memories</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-sm rounded-xl p-6 border border-purple-500/30">
              <div className="text-4xl mb-4">🏆</div>
              <h3 className="text-xl font-bold mb-2">Epic Boss Defeats</h3>
              <p className="text-gray-300">Share your most challenging boss battle victories and the strategies that led to success!</p>
            </div>

            <div className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-sm rounded-xl p-6 border border-blue-500/30">
              <div className="text-4xl mb-4">🎮</div>
              <h3 className="text-xl font-bold mb-2">Setup Showcases</h3>
              <p className="text-gray-300">Show off your gaming battlestation, from RGB setups to retro collections!</p>
            </div>

            <div className="bg-gradient-to-r from-green-500/20 to-blue-500/20 backdrop-blur-sm rounded-xl p-6 border border-green-500/30">
              <div className="text-4xl mb-4">👥</div>
              <h3 className="text-xl font-bold mb-2">Squad Moments</h3>
              <p className="text-gray-300">Capture those unforgettable moments with your gaming crew and team victories!</p>
            </div>

            <div className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-sm rounded-xl p-6 border border-yellow-500/30">
              <div className="text-4xl mb-4">📸</div>
              <h3 className="text-xl font-bold mb-2">Photo Mode Masters</h3>
              <p className="text-gray-300">Stunning in-game photography and artistic captures from your favorite games!</p>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <button className="bg-purple-500 hover:bg-purple-600 text-white font-bold px-8 py-3 rounded-full transition-colors mr-4">
            Upload Memory
          </button>
          <button className="bg-pink-500 hover:bg-pink-600 text-white font-bold px-8 py-3 rounded-full transition-colors">
            View All Memories
          </button>
        </div>
      </div>
    </div>
  )
}

export default MemoriesPage